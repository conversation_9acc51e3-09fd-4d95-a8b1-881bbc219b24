<!--
  Test Login Page
  
  Simple test page to verify login functionality works correctly.
  This page tests the authentication flow with known credentials.
-->

<script lang="ts">
	import { Button } from "$lib/components/ui/button";
	import { Input } from "$lib/components/ui/input";
	import { Label } from "$lib/components/ui/label";
	import { getAuthContext } from "$lib/stores/auth.svelte";
	import { goto } from "$app/navigation";

	// Get authentication store from context
	const authStore = getAuthContext();

	// Test credentials
	let email = "<EMAIL>";
	let password = "demo123456";

	/**
	 * Test login function
	 */
	async function testLogin(): Promise<void> {
		console.log("Testing login with:", { email, password: "***" });
		
		// Clear any previous errors
		authStore.clearError();

		// Attempt login
		const success = await authStore.login({ email, password });

		if (success) {
			console.log("Login successful!");
			alert("Login successful! Redirecting to dashboard...");
			goto("/dashboard");
		} else {
			console.log("Login failed:", authStore.error);
			alert(`Login failed: ${authStore.error}`);
		}
	}

	/**
	 * Test logout function
	 */
	function testLogout(): void {
		authStore.logout();
		alert("Logged out successfully!");
	}
</script>

<div class="container mx-auto max-w-md p-8">
	<h1 class="text-2xl font-bold mb-6">Login Test Page</h1>
	
	<!-- Authentication Status -->
	<div class="mb-6 p-4 border rounded">
		<h2 class="text-lg font-semibold mb-2">Auth Status</h2>
		<p><strong>Authenticated:</strong> {authStore.isAuthenticated}</p>
		<p><strong>Loading:</strong> {authStore.isLoading}</p>
		<p><strong>User:</strong> {authStore.user?.email || "None"}</p>
		{#if authStore.error}
			<p class="text-red-600"><strong>Error:</strong> {authStore.error}</p>
		{/if}
	</div>

	<!-- Test Credentials Form -->
	<div class="space-y-4">
		<div>
			<Label for="email">Email</Label>
			<Input
				id="email"
				type="email"
				bind:value={email}
				placeholder="<EMAIL>"
			/>
		</div>

		<div>
			<Label for="password">Password</Label>
			<Input
				id="password"
				type="password"
				bind:value={password}
				placeholder="demo123456"
			/>
		</div>

		<div class="flex gap-2">
			<Button
				onclick={testLogin}
				disabled={authStore.isLoading}
				class="flex-1"
			>
				{#if authStore.isLoading}
					Testing Login...
				{:else}
					Test Login
				{/if}
			</Button>

			<Button
				onclick={testLogout}
				variant="outline"
				disabled={authStore.isLoading}
			>
				Test Logout
			</Button>
		</div>
	</div>

	<!-- Debug Information -->
	<div class="mt-8 p-4 bg-gray-100 rounded text-sm">
		<h3 class="font-semibold mb-2">Debug Info</h3>
		<p><strong>Test Credentials:</strong></p>
		<p>Email: <EMAIL></p>
		<p>Password: demo123456</p>
		<p class="mt-2"><strong>API Endpoint:</strong> https://refactored-memory.onrender.com/auth/login/</p>
	</div>

	<!-- Navigation -->
	<div class="mt-6 text-center">
		<a href="/login" class="text-blue-600 hover:underline">Go to Real Login Page</a>
	</div>
</div>
